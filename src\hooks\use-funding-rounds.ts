import { useApiQuery, useApiMutation, useInvalidateQueries } from "./useApi";
import { GetAllResponses, queryKeys } from "@/lib/types/api";

/**
 * Type definition for a funding round
 */
export type FundingRound = {
  id: string;
  roundNumber: number;
  openDate: string;
  closeDate: string;
  createdAt?: string;
  updatedAt?: string;
};

/**
 * Hook for fetching all funding rounds
 */
export function useFundingRounds() {
  return useApiQuery<GetAllResponses<FundingRound>>(
    queryKeys.fundingRounds(),
    "/fundingRounds",
    undefined,
    {
      // Keep funding rounds data fresh for a reasonable time (5 minutes)
      staleTime: 5 * 60 * 1000,
    }
  );
}

/**
 * Hook for fetching a single funding round by ID
 */
export function useFundingRound(id: string) {
  return useApiQuery<FundingRound>(
    queryKeys.fundingRound(id),
    `/fundingRounds/${id}`,
    undefined,
    {
      // Don't fetch if no ID is provided
      enabled: !!id,
    }
  );
}

/**
 * Hook for creating a new funding round
 */
export function useCreateFundingRound() {
  const invalidateQueries = useInvalidateQueries();

  return useApiMutation<
    FundingRound,
    Omit<FundingRound, "id" | "createdAt" | "updatedAt">
  >("/fundingRounds", "POST", {
    onSuccess: () => {
      // Invalidate funding rounds list to refetch data
      invalidateQueries([queryKeys.fundingRounds()]);
    },
  });
}

/**
 * Hook for updating an existing funding round
 */
export function useUpdateFundingRound() {
  const invalidateQueries = useInvalidateQueries();

  return useApiMutation<
    FundingRound,
    { id: string; data: Partial<FundingRound> }
  >("/fundingRounds", "PUT", {
    onSuccess: (data, variables) => {
      // Invalidate both the specific funding round and the list
      invalidateQueries([
        queryKeys.fundingRound(variables.id),
        queryKeys.fundingRounds(),
      ]);
    },
  });
}

/**
 * Hook for deleting a funding round
 */
export function useDeleteFundingRound() {
  const invalidateQueries = useInvalidateQueries();

  return useApiMutation<void, string>("/fundingRounds", "DELETE", {
    onSuccess: (data, id) => {
      // Invalidate both the specific funding round and the list
      invalidateQueries([
        queryKeys.fundingRound(id),
        queryKeys.fundingRounds(),
      ]);
    },
  });
}
