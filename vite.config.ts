import { resolve } from "path";
import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load env variables based on mode
  const env = loadEnv(mode, process.cwd(), "");

  // Extract API URL from environment variables
  const apiUrl = env.VITE_API_URL || "";

  console.log(`Development mode: ${mode}`);
  console.log(`API URL for proxy: ${apiUrl}`);

  return {
    plugins: [react(), tailwindcss()],
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
      },
    },
    // Configure server options including proxy
    server: {
      port: 5173,

      proxy: {
        // Proxy all /api requests to the actual backend
        "/api/v1": {
          target: "http://localhost:8080",
          changeOrigin: true,
        },
      },
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Handle prismjs and its languages
            if (id.includes("node_modules/prismjs")) {
              return "vendor-prismjs";
            }

            // Handle react-syntax-highlighter
            if (id.includes("node_modules/react-syntax-highlighter")) {
              return "vendor-syntax-highlighter";
            }

            // Handle react-simple-code-editor
            if (id.includes("node_modules/react-simple-code-editor")) {
              return "vendor-code-editor";
            }

            // Handle other vendor chunks
            if (
              id.includes("node_modules/react/") ||
              id.includes("node_modules/react-dom/") ||
              id.includes("node_modules/react-router-dom/")
            ) {
              return "vendor-react";
            }

            if (id.includes("node_modules/@radix-ui/react-")) {
              return "vendor-ui";
            }

            if (id.includes("node_modules/@dnd-kit/")) {
              return "vendor-dnd";
            }

            if (
              id.includes("node_modules/react-hook-form") ||
              id.includes("node_modules/@hookform/")
            ) {
              return "vendor-form";
            }

            if (
              id.includes("node_modules/class-variance-authority") ||
              id.includes("node_modules/clsx") ||
              id.includes("node_modules/tailwind-merge") ||
              id.includes("node_modules/lucide-react")
            ) {
              return "vendor-utils";
            }

            // Split form builder components
            if (
              id.includes("/components/form-builder/") &&
              !id.includes("index")
            ) {
              return "form-builder-components";
            }

            // Split hooks
            if (id.includes("/hooks/") && !id.includes("index")) {
              return "hooks";
            }

            // Return undefined for default chunking behavior
            return undefined;
          },
        },
      },
      chunkSizeWarningLimit: 1000, // Increase the warning limit
    },
  };
});
