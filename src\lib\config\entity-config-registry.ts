import {
  EntityConfig,
  FormPageConfig,
  ListPageConfig,
} from "@/lib/types/page-config";
import { Link } from "lucide-react";

/**
 * Registry of all entity configurations
 * This is the single source of truth for entity configurations
 */
const entityConfigs: Record<string, EntityConfig> = {
  // Project entity configuration
  Project: {
    // Base entity information
    id: "projects",
    entityName: "Project",
    title: "Projects",
    description: "Manage your projects",
    permissions: ["admin"],
    endpoints: {
      list: "/projects",
      get: "/projects",
      create: "/projects",
      update: "/projects",
      delete: "/projects",
    },

    // Fields definition (used for both form and list)
    fields: [
      {
        id: "name",
        name: "name",
        type: "text",
        label: "Project Name",
        required: true,
      },
      {
        id: "description",
        name: "description",
        type: "text",
        label: "Description",
      },
      {
        id: "status",
        name: "status",
        type: "select",
        label: "Status",
        options: [
          { label: "Application Draft", value: "Application Draft" },
          { label: "Application Submitted", value: "application-submitted" },
          {
            label: "Application Under Assessment - Internal Review",
            value: "application-under-assessment-internal-review",
          },
          {
            label: "Application Under Assessment - Clarification Raised",
            value: "application-under-assessment-clarification-raised",
          },
          {
            label: "Application Under Assessment - Assessed",
            value: "application-under-assessment-assessed",
          },
          {
            label: "Application Under Assessment - Approved",
            value: "application-under-assessment-approved",
          },
          {
            label: "Application Under Assessment - Awarded",
            value: "application-under-assessment-awarded",
          },
          { label: "Funding Awarded", value: "funding-awarded" },
          { label: "Project Complete", value: "project-complete" },
          { label: "Project Commissioned", value: "project-commissioned" },
        ],
      },

      {
        id: "organisationType",
        name: "organisationType",
        type: "select",
        label: "Organisation Type",
        required: true,

        options: [
          { label: "Local", value: "Local" },
          { label: "Authority", value: "authority" },
          { label: "Private", value: "private" },
          {
            label: "Inter-departmental transfer",
            value: "inter-departmental-transfer",
          },
          { label: "Other", value: "other" },
        ],
      },
      {
        id: "applicationType",
        name: "applicationType",
        type: "select",
        label: "Application Type",
        required: true,
        options: [
          { label: "Capital", value: "Capital" },
          { label: "Revenue", value: "revenue" },
        ],
      },
      {
        id: "projectRef",
        name: "projectRef",
        type: "text",
        label: "Project Ref",
        required: true,
      },
      {
        id: "applicantOrganisation",
        name: "applicantOrganisation",
        type: "text",
        label: "Applicant Organisation",
        required: true,
      },
    ],

    // List view configuration
    listConfig: {
      columns: [
        {
          id: "id",
          header: "ID",
          accessorKey: "id",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 120,
        },
        {
          id: "name",
          header: "Project Name",
          accessorKey: "name",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 200,
        },
        {
          id: "status",
          header: "Status",
          accessorKey: "status",
          type: "status",
          enableSorting: true,
          enableColumnFilter: true,
          width: 150,
          formatOptions: {
            showIcon: true,
            size: "md",
          },
        },
        {
          id: "projectRef",
          header: "Project Ref",
          accessorKey: "projectRef",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "applicationType",
          header: "Application Type",
          accessorKey: "applicationType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },

        {
          id: "organisationType",
          header: "Organisation Type",
          accessorKey: "organisationType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "applicantOrganisation",
          header: "Applicant Organisation",
          accessorKey: "applicantOrganisation",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
      ],
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
        {
          id: "attach",
          label: "Attach to Funding Round",
          icon: Link,
          action: "custom",
          handler: (row) => {
            console.log(row);
          },
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "name", desc: false }],
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: true,
      defaultPinnedColumns: {
        id: 1,
        name: 2,
      },
    },

    // Form view configuration
    formConfig: {
      submitButtonText: "Save Project",
      cancelButtonText: "Cancel",
      successMessage: "Project saved successfully!",
      errorMessage: "Failed to save project. Please try again.",
      redirectAfterSubmit: "/projects",
    },
  },

  // FundingRound entity configuration
  FundingRound: {
    // Base entity information
    id: "funding-rounds",
    entityName: "FundingRound",
    title: "Funding Rounds",
    description: "Manage funding rounds",
    permissions: ["admin"],
    endpoints: {
      list: "/fundingRounds",
      get: "/fundingRounds",
      create: "/fundingRounds",
      update: "/fundingRounds",
      delete: "/fundingRounds",
    },

    // Fields definition (used for both form and list)
    fields: [
      {
        id: "roundNumber",
        name: "roundNumber",
        type: "number",
        label: "Round Number",
        required: true,
        validations: [
          {
            rule: "required",
            message: "Round Number is required",
          },
        ],
      },
      {
        id: "openDate",
        name: "openDate",
        type: "date",
        label: "Open Date",
        required: true,
        validations: [
          {
            rule: "required",
            message: "Open Date is required",
          },
        ],
      },
      {
        id: "closeDate",
        name: "closeDate",
        type: "date",
        label: "Submission Deadline Date",
        required: true,
        validations: [
          {
            rule: "required",
            message: "Submission Deadline Date is required",
          },
        ],
      },
    ],

    // List view configuration
    listConfig: {
      columns: [
        {
          id: "id",
          header: "ID",
          accessorKey: "id",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 120,
        },
        {
          id: "roundNumber",
          header: "Round Number",
          accessorKey: "roundNumber",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 150,
        },
        {
          id: "openDate",
          header: "Open Date",
          accessorKey: "openDate",
          type: "date",
          enableSorting: true,
          enableColumnFilter: true,
          width: 200,
          formatOptions: {
            variant: "medium",
            showTime: true,
          },
        },
        {
          id: "closeDate",
          header: "Submission Deadline",
          accessorKey: "closeDate",
          type: "date",
          enableSorting: true,
          enableColumnFilter: true,
          width: 250,
          formatOptions: {
            variant: "medium",
            showTime: true,
          },
        },
      ],
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "roundNumber", desc: false }],
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: true,
      defaultPinnedColumns: {
        id: 1,
        roundNumber: 2,
      },
    },

    // Form view configuration
    formConfig: {
      submitButtonText: "Save Funding Round",
      cancelButtonText: "Cancel",
      successMessage: "Funding Round saved successfully!",
      errorMessage: "Failed to save funding round. Please try again.",
      redirectAfterSubmit: "/funding-rounds",
    },
  },

  // Add more entity configurations as needed
};

/**
 * Get the base entity configuration
 */
export function getEntityConfig(entityName: string): EntityConfig | null {
  // First try direct lookup (exact match)
  if (entityConfigs[entityName]) {
    return entityConfigs[entityName];
  }

  // Try with first letter capitalized (for case-insensitive lookup)
  const normalizedEntityName =
    entityName.charAt(0).toUpperCase() + entityName.slice(1);
  if (entityConfigs[normalizedEntityName]) {
    return entityConfigs[normalizedEntityName];
  }

  // get entityConfig keys
  const entityConfigKeys = Object.keys(entityConfigs).filter(
    (key) => key.toLowerCase() === entityName.toLowerCase()
  );

  if (entityConfigKeys.length > 0) {
    return entityConfigs[entityConfigKeys[0]];
  }

  return null;
}

/**
 * Create a list page configuration from the base entity configuration
 */
export function createListPageConfig(
  entityName: string
): ListPageConfig | null {
  const config = getEntityConfig(entityName);
  if (!config) return null;

  return {
    id: `${config.id}`,
    type: "list",
    endpoints: config.endpoints,
    title: config.title,
    description: config.description,
    entityName: config.entityName,
    permissions: config.permissions,
    columns: config.listConfig.columns,
    actions: config.listConfig.actions,
    defaultPageSize: config.listConfig.defaultPageSize,
    enableGlobalFilter: config.listConfig.enableGlobalFilter,
    enableColumnFilters: config.listConfig.enableColumnFilters,
    enablePinning: config.listConfig.enablePinning,
    createFormConfig: createFormPageConfig(entityName) || undefined,
  };
}

/**
 * Create a form page configuration from the base entity configuration
 */
export function createFormPageConfig(
  entityName: string
): FormPageConfig | null {
  const config = getEntityConfig(entityName);
  if (!config) return null;

  return {
    id: `${config.id}`,
    type: "form",
    title: `${config.entityName} Details`,
    endpoints: config.endpoints,
    description: `Edit ${config.entityName.toLowerCase()} information`,
    entityName: config.entityName,
    permissions: config.permissions,
    fields: config.fields,
    submitButtonText: config.formConfig.submitButtonText,
    cancelButtonText: config.formConfig.cancelButtonText,
    successMessage: config.formConfig.successMessage,
    errorMessage: config.formConfig.errorMessage,
    redirectAfterSubmit: config.formConfig.redirectAfterSubmit,
  };
}

export default entityConfigs;
