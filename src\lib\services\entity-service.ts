import { SortingState } from "@tanstack/react-table";
import { TableFilter, TableData, PaginationState } from "./table-service";
import { apiClient } from "../api/api-client";
import { GetAllResponses } from "../types/api";

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// In-memory storage for entities
const entityStorage: Record<string, Record<string, any>[]> = {};

/**
 * Service for handling entity data operations
 */
export const EntityService = {
  /**
   * Get all entities of a specific type with pagination, sorting, and filtering
   */

  buildParams(
    filter: {
      globalFilter?: string;
      columnFilters?: { id: string; value: string }[];
    },
    sorting?: { id: string; desc: boolean }[],
    pagination?: { pageIndex: number; pageSize: number }
  ): Record<string, string> {
    const params: Record<string, string> = {};

    // Global search term
    if (filter.globalFilter) {
      params["searchTerm"] = filter.globalFilter;
    }

    // Column-specific filters
    if (filter.columnFilters) {
      filter.columnFilters.forEach(({ id, value }) => {
        params[id] = value;
      });
    }

    // Sorting (as JSON-encoded string)
    if (sorting && sorting.length > 0) {
      params["sort"] = JSON.stringify(sorting.map((s) => s.id)); // You can adjust this format if backend expects more
      params["desc"] = JSON.stringify(sorting.map((s) => s.desc));
    }

    // Pagination
    if (pagination) {
      params["page"] = pagination.pageIndex.toString(); // Assuming backend pages start at 1
      params["size"] = pagination.pageSize.toString();
    }

    return params;
  },

  getEntities: async <T extends Record<string, any>>(
    entityName: string,
    endpoint: string,
    pagination: PaginationState,
    sorting: SortingState,
    filter: TableFilter
  ): Promise<TableData<T>> => {
    try {
      const params = EntityService.buildParams(filter, sorting, pagination);
      const response = await apiClient.get<GetAllResponses<T>>(endpoint, {
        params,
      });
      return {
        data: response.data.content,
        pageCount: response.data.number,
        totalCount: response.data.totalElements,
      };
    } catch (error) {
      console.error("Error fetching projects:", error);
      return await EntityService.getAllMockedData(
        entityName,
        pagination,
        sorting,
        filter
      );
      //throw error;
    }
  },

  getAllMockedData: async <T extends Record<string, any>>(
    entityName: string,
    pagination: PaginationState,
    sorting: SortingState,
    filter: TableFilter
  ): Promise<TableData<T>> => {
    // Use a shorter delay to prevent UI flicker but still simulate async behavior
    await delay(100);

    // Get entities from storage or return empty array
    const entities = (entityStorage[entityName] || []) as T[];

    // Apply filters
    let filteredData = filterData(entities, filter);

    // Apply sorting
    const sortedData = sortData(filteredData, sorting);

    // Apply pagination
    const { pageIndex, pageSize } = pagination;
    const start = pageIndex * pageSize;
    const end = start + pageSize;
    const paginatedData = sortedData.slice(start, end);

    // Calculate page count with a minimum of 1 to prevent division by zero
    const effectivePageSize = Math.max(1, pageSize);
    const pageCount = Math.max(
      1,
      Math.ceil(filteredData.length / effectivePageSize)
    );

    return {
      data: paginatedData,
      pageCount: pageCount,
      totalCount: filteredData.length,
    };
  },

  /**
   * Get a specific entity by ID
   */
  getEntityById: async <T extends Record<string, any>>(
    entityName: string,
    endpoint: string,
    id: string
  ): Promise<T | null> => {
    console.log("🚀 ~ entityName:", entityName);
    try {
      const response = await apiClient.get<T>(`/${endpoint}/${id}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching project:", error);
      return null;
    }
  },

  /**
   * Create a new entity
   */
  createEntity: async <T extends Record<string, any>>(
    entityName: string,
    endpoint: string,
    data: Omit<T, "id" | "createdAt" | "updatedAt">
  ): Promise<T> => {
    console.log("🚀 ~ entityName:", entityName);
    console.log("🚀 ~ data:", data);
    const response = await apiClient.post<T>(`${endpoint}`, data);
    return response.data;
  },

  /**
   * Update an existing entity
   */
  updateEntity: async <T extends Record<string, any>>(
    entityName: string,
    endpoint: string,
    id: string,
    data: Partial<T>
  ): Promise<T | null> => {
    console.log("🚀 ~ entityName:", entityName);
    const response = await apiClient.put<T>(`${endpoint}/${id}`, data);
    return response.data;
  },

  /**
   * Delete an entity
   */
  deleteEntity: async (
    entityName: string,
    endpoint: string,
    id: string
  ): Promise<boolean> => {
    console.log("🚀 ~ deleteEntity: ~ entityName:", entityName);
    try {
      await apiClient.delete(`${endpoint}/${id}`);
      return true;
    } catch (error) {
      console.log("🚀 ~ error:", error);
      return false;
    }
  },

  /**
   * Initialize entity storage with mock data
   */
  initializeEntityStorage: (
    entityName: string,
    mockData: Record<string, any>[]
  ): void => {
    entityStorage[entityName] = mockData;
  },
};

// Helper function to filter data
function filterData<T extends Record<string, unknown>>(
  data: T[],
  filter: TableFilter
): T[] {
  let filtered = [...data];

  // Apply global filter
  if (filter.globalFilter) {
    const searchTerm = filter.globalFilter.toLowerCase();
    filtered = filtered.filter((item) => {
      return Object.values(item).some((value) => {
        if (value === null || value === undefined) return false;
        return String(value).toLowerCase().includes(searchTerm);
      });
    });
  }

  // Apply column filters
  if (filter.columnFilters.length) {
    filter.columnFilters.forEach((columnFilter) => {
      if (columnFilter.value) {
        const searchTerm = columnFilter.value.toLowerCase();
        filtered = filtered.filter((item) => {
          const value = (item as any)[columnFilter.id];
          if (value === null || value === undefined) return false;
          return String(value).toLowerCase().includes(searchTerm);
        });
      }
    });
  }

  return filtered;
}

// Helper function to sort data
function sortData<T>(data: T[], sorting: SortingState): T[] {
  if (!sorting.length) return data;

  return [...data].sort((a, b) => {
    for (const sort of sorting) {
      const { id, desc } = sort;
      const aValue = (a as any)[id];
      const bValue = (b as any)[id];

      // Handle different data types
      if (typeof aValue === "string" && typeof bValue === "string") {
        const comparison = aValue.localeCompare(bValue);
        if (comparison !== 0) {
          return desc ? -comparison : comparison;
        }
      } else if (typeof aValue === "number" && typeof bValue === "number") {
        if (aValue !== bValue) {
          return desc ? bValue - aValue : aValue - bValue;
        }
      } else if (aValue instanceof Date && bValue instanceof Date) {
        const timeA = aValue.getTime();
        const timeB = bValue.getTime();
        if (timeA !== timeB) {
          return desc ? timeB - timeA : timeA - timeB;
        }
      } else {
        // Fallback for other types
        const valueA = String(aValue);
        const valueB = String(bValue);
        const comparison = valueA.localeCompare(valueB);
        if (comparison !== 0) {
          return desc ? -comparison : comparison;
        }
      }
    }
    return 0;
  });
}
