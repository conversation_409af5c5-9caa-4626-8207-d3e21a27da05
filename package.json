{"name": "hnes-form-builder", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "version": "node scripts/generate-conventional-changelog.js && git add CHANGELOG.md", "changelog": "node scripts/generate-conventional-changelog.js", "changelog:all": "node scripts/generate-conventional-changelog.js --all", "commit": "cz", "prepare": "husky"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.8", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash-es": "^4.17.21", "lucide-react": "^0.507.0", "prismjs": "^1.30.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-router-dom": "^7.5.3", "react-simple-code-editor": "^0.14.1", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.22.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.3", "@types/prismjs": "^1.26.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "husky": "^9.1.7", "postcss": "^8.5.3", "shadcn": "^2.5.0", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.9", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.4"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}