# HNES Form Builder

A modern, flexible form builder application for creating, managing, and filling out dynamic forms with advanced features.

## Overview

The HNES Form Builder is a React-based web application that allows administrators to create complex, multi-step forms with validation, conditional rendering, and data grid components. Applicants can view and fill out these forms through a user-friendly interface.

![HNES Form Builder](https://via.placeholder.com/800x400?text=HNES+Form+Builder)

## Features

### Form Building

- **Drag and Drop Interface**: Intuitive drag-and-drop functionality for form creation
- **Multi-step Forms**: Create forms with configurable steps and sections
- **Conditional Rendering**: Show/hide form components based on user inputs
- **Data Grid Components**: Excel-style data grids with configurable headers and editable cells
- **Validation Support**: Comprehensive form validation capabilities
- **JSON Schema Support**: Generate forms from existing JSON schemas

### User Experience

- **Responsive Design**: Optimized for all device sizes
- **Collapsible Navigation**: Sidebar navigation that can be collapsed for more screen space
- **Role-based Access**: Different interfaces for administrators and applicants

### Technical Features

- **Modern Stack**: Built with React, TypeScript, and Vite
- **Performance Optimized**: Uses React best practices including useMemo, memo, and useCallback
- **Tailwind CSS**: Styled with Tailwind CSS for consistent design
- **shadcn UI Components**: Leverages shadcn UI for accessible, reusable components
- **Code Splitting**: Implements dynamic imports for optimized bundle size

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:

   ```bash
   git clone http://gitlab.dom.tti/gemserv/Scheme-Manager-Frontend.git
   cd Scheme-Manager-Frontend
   ```

2. Install dependencies:

   ```bash
   npm install
   # or
   yarn install
   ```

3. Start the development server:

   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

## Usage

### Administrator View

- Create new forms using the drag-and-drop form builder
- Configure form validation rules and conditional rendering
- Manage existing forms and view submissions

### Applicant View

- View available applications
- Fill out forms with real-time validation
- Save progress and submit completed applications

## Project Structure

```
src/
├── components/         # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── form-builder/   # Form builder components
│   ├── layout/         # Layout components
│   └── ui/             # UI components (shadcn)
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── lib/                # Utility functions and services
│   ├── services/       # API services
│   └── types/          # TypeScript type definitions
├── pages/              # Application pages
└── App.tsx             # Main application component
```

## Contributing

1. Create a feature branch from `develop`
2. Make your changes
3. Submit a merge request to the `develop` branch

## Versioning and Changelog

This project follows [Semantic Versioning](https://semver.org/) and maintains a changelog based on the [Conventional Commits](https://www.conventionalcommits.org/) specification.

### Conventional Commits

We use the Conventional Commits specification for commit messages, which provides a structured format that's both human and machine-readable. This allows us to automatically generate changelogs from commit messages.

The basic structure of a conventional commit is:

```
<type>(<scope>): <description>

<body>

<footer>
```

Where:

- **type**: Describes the kind of change (e.g., feat, fix, docs)
- **scope** (optional): Describes what part of the codebase is affected
- **description**: A short summary of the change
- **body** (optional): Detailed description of the change
- **footer** (optional): Information about breaking changes or issue references

Common types include:

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code changes that neither fix bugs nor add features
- `perf`: Performance improvements
- `test`: Adding or fixing tests
- `build`: Changes to the build system
- `ci`: Changes to CI configuration
- `chore`: Other changes that don't modify src or test files

For more details, see our [Conventional Commits Guide](docs/conventional-commits.md).

### Creating Commits

To create a commit that follows the Conventional Commits specification, you can use our interactive commit tool:

```bash
npm run commit
```

This will guide you through creating a properly formatted commit message.

### Automatic Changelog Generation

The changelog is automatically generated from conventional commit messages. To update the changelog:

```bash
# Update the changelog with new entries
npm run changelog

# Regenerate the entire changelog
npm run changelog:all
```

### Releasing a New Version

When releasing a new version, use the npm version command:

```bash
npm version patch  # for bug fixes
npm version minor  # for new features
npm version major  # for breaking changes
```

This will:

1. Update the version in package.json
2. Update the CHANGELOG.md file automatically based on conventional commits
3. Create a git commit with the new version
4. Create a git tag for the version

## License

Proprietary - All rights reserved
