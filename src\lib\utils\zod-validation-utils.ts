import { z } from "zod";
import {
  FormComponent,
  FormComponentValidation,
  ValidationRule,
  TextComponent,
  NumberComponent,
  DateComponent,
  DateTimeComponent,
  SelectComponent,
  CheckboxComponent,
  RadioComponent,
  DataGridComponent,
  StepComponent,
  SectionComponent,
  InfoTextComponent,
} from "../schemas/form-schemas";

// Add getValidationRules method to ZodType prototype
declare module "zod" {
  interface ZodType<Output, Def, Input> {
    getValidationRules(): Record<string, any>;
  }
}

// Implementation of getValidationRules
z.ZodType.prototype.getValidationRules = function (): Record<string, any> {
  // Convert Zod schema to React Hook Form validation rules
  const rules: Record<string, any> = {};

  // Required rule
  if (this._def.typeName === "ZodString" && this._def.checks) {
    // Min length
    const minCheck = this._def.checks.find(
      (check: any) => check.kind === "min"
    );
    if (minCheck) {
      rules.minLength = {
        value: minCheck.value,
        message: `Minimum length is ${minCheck.value} characters`,
      };
    }

    // Max length
    const maxCheck = this._def.checks.find(
      (check: any) => check.kind === "max"
    );
    if (maxCheck) {
      rules.maxLength = {
        value: maxCheck.value,
        message: `Maximum length is ${maxCheck.value} characters`,
      };
    }

    // Pattern
    const regexCheck = this._def.checks.find(
      (check: any) => check.kind === "regex"
    );
    if (regexCheck) {
      rules.pattern = {
        value: regexCheck.regex,
        message: regexCheck.message || "Invalid format",
      };
    }
  }

  // Required
  if (
    this._def.typeName !== "ZodOptional" &&
    this._def.typeName !== "ZodNullable"
  ) {
    rules.required = "This field is required";
  }

  return rules;
};

// Re-export the types for easier access
export type {
  FormComponent,
  TextComponent,
  NumberComponent,
  DateComponent,
  DateTimeComponent,
  SelectComponent,
  CheckboxComponent,
  RadioComponent,
  DataGridComponent,
  StepComponent,
  SectionComponent,
  InfoTextComponent,
  ValidationRule,
};

// Email validation regex pattern
export const EMAIL_PATTERN = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

// URL validation regex pattern
export const URL_PATTERN =
  /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]+)*\/?$/;

/**
 * Type guard for text component
 */
export function isTextComponent(
  component: FormComponent
): component is TextComponent {
  return component.type === "text";
}

/**
 * Type guard for number component
 */
export function isNumberComponent(
  component: FormComponent
): component is NumberComponent {
  return component.type === "number";
}

/**
 * Type guard for date component
 */
export function isDateComponent(
  component: FormComponent
): component is DateComponent {
  return component.type === "date";
}

/**
 * Type guard for datetime component
 */
export function isDateTimeComponent(
  component: FormComponent
): component is DateTimeComponent {
  return component.type === "datetime";
}

/**
 * Type guard for select component
 */
export function isSelectComponent(
  component: FormComponent
): component is SelectComponent {
  return component.type === "select";
}

/**
 * Type guard for checkbox component
 */
export function isCheckboxComponent(
  component: FormComponent
): component is CheckboxComponent {
  return component.type === "checkbox";
}

/**
 * Type guard for radio component
 */
export function isRadioComponent(
  component: FormComponent
): component is RadioComponent {
  return component.type === "radio";
}

/**
 * Type guard for datagrid component
 */
export function isDataGridComponent(
  component: FormComponent
): component is DataGridComponent {
  return component.type === "datagrid";
}

/**
 * Type guard for step component
 */
export function isStepComponent(
  component: FormComponent
): component is StepComponent {
  return component.type === "step";
}

/**
 * Type guard for section component
 */
export function isSectionComponent(
  component: FormComponent
): component is SectionComponent {
  return component.type === "section";
}

/**
 * Type guard for infoText component
 */
export function isInfoTextComponent(
  component: FormComponent
): component is InfoTextComponent {
  return component.type === "infoText";
}

/**
 * Type guard for container components (step or section)
 */
export function isContainerComponent(
  component: FormComponent
): component is StepComponent | SectionComponent {
  return component.type === "step" || component.type === "section";
}

/**
 * Creates a Zod schema for validating a form component's value
 */
export function createComponentValidationSchema(
  component: FormComponent
): z.ZodTypeAny {
  // Start with a base schema based on component type
  let schema: z.ZodTypeAny;

  switch (component.type) {
    case "text":
      schema = z.string();
      if (component.minLength !== undefined) {
        schema = (schema as z.ZodString).min(
          component.minLength,
          `Minimum length is ${component.minLength} characters`
        );
      }
      if (component.maxLength !== undefined) {
        schema = (schema as z.ZodString).max(
          component.maxLength,
          `Maximum length is ${component.maxLength} characters`
        );
      }
      break;

    case "number":
      schema = z.coerce.number();
      if (component.min !== undefined) {
        schema = (schema as z.ZodNumber).min(
          component.min,
          `Minimum value is ${component.min}`
        );
      }
      if (component.max !== undefined) {
        schema = (schema as z.ZodNumber).max(
          component.max,
          `Maximum value is ${component.max}`
        );
      }
      break;

    case "date":
    case "datetime":
      schema = z.string().refine((val) => !isNaN(Date.parse(val)), {
        message: "Invalid date format",
      });
      if (component.min) {
        schema = schema.refine(
          (val) => new Date(val) >= new Date(component.min as string),
          {
            message: `Date must be on or after ${component.min}`,
          }
        );
      }
      if (component.max) {
        schema = schema.refine(
          (val) => new Date(val) <= new Date(component.max as string),
          {
            message: `Date must be on or before ${component.max}`,
          }
        );
      }
      break;

    case "select":
      if (component.multiple) {
        schema = z.array(z.string());
      } else {
        schema = z.string();
      }
      // Validate against available options
      if (component.options && component.options.length > 0) {
        const validValues = component.options.map((opt) => opt.value);
        if (component.multiple) {
          schema = (schema as z.ZodArray<z.ZodString>).refine(
            (vals) => vals.every((val) => validValues.includes(val)),
            { message: "Selected values must be from the available options" }
          );
        } else {
          schema = (schema as z.ZodString).refine(
            (val) => validValues.includes(val),
            { message: "Selected value must be from the available options" }
          );
        }
      }
      break;

    case "checkbox":
      if (component.multiple) {
        schema = z.array(z.string());
      } else {
        schema = z.boolean();
      }
      break;

    case "radio":
      schema = z.string();
      // Validate against available options
      if (component.options && component.options.length > 0) {
        const validValues = component.options.map((opt) => opt.value);
        schema = schema.refine((val) => validValues.includes(val), {
          message: "Selected value must be from the available options",
        });
      }
      break;

    case "datagrid":
      // For datagrid, we'll validate the entire grid data as a record
      schema = z.record(z.string(), z.string());
      break;

    case "step":
    case "section":
    case "infoText":
      // Container components don't have direct values to validate
      schema = z.any();
      break;

    default:
      schema = z.any();
  }

  // Apply required validation if needed
  if (component.required) {
    if (component.type === "checkbox" && !component.multiple) {
      // For single checkboxes, required means it must be true
      schema = (schema as z.ZodBoolean).refine((val) => val === true, {
        message: "This field is required",
      });
    } else if (component.type === "select" && component.multiple) {
      // For multi-select, required means at least one item selected
      schema = (schema as z.ZodArray<z.ZodString>).refine(
        (val) => val.length > 0,
        {
          message: "At least one option must be selected",
        }
      );
    } else if (component.type === "datagrid") {
      // For datagrid, we don't apply required at this level
      // It's handled per cell
    } else {
      // For other types, make the schema non-nullable and non-optional
      schema = schema.refine(
        (val) => val !== null && val !== undefined && val !== "",
        {
          message: "This field is required",
        }
      );
    }
  } else {
    // If not required, allow null or undefined
    schema = schema.nullable().optional();
  }

  // Apply custom validations from the component
  if (component.validations && component.validations.length > 0) {
    component.validations.forEach((validation) => {
      schema = applyCustomValidation(schema, validation, component.type);
    });
  }

  return schema;
}

/**
 * Apply a custom validation rule to a Zod schema
 */
function applyCustomValidation(
  schema: z.ZodTypeAny,
  validation: FormComponentValidation,
  componentType: FormComponent["type"]
): z.ZodTypeAny {
  switch (validation.rule) {
    case "email":
      if (componentType === "text") {
        return (schema as z.ZodString).regex(
          EMAIL_PATTERN,
          validation.message ?? "Invalid email address"
        );
      }
      break;

    case "url":
      if (componentType === "text") {
        return (schema as z.ZodString).regex(
          URL_PATTERN,
          validation.message ?? "Invalid URL"
        );
      }
      break;

    case "pattern":
      if (componentType === "text" && validation.value) {
        return (schema as z.ZodString).regex(
          new RegExp(validation.value as string),
          validation.message ?? "Invalid format"
        );
      }
      break;
  }

  // If we didn't apply a specific validation, return the original schema
  return schema;
}

/**
 * Create a Zod schema for validating an entire form
 */
export function createFormValidationSchema(
  components: FormComponent[]
): z.ZodObject<any> {
  const schemaMap: Record<string, z.ZodTypeAny> = {};

  components.forEach((component) => {
    // Skip container components as they don't have direct values
    if (
      component.type !== "step" &&
      component.type !== "section" &&
      component.type !== "infoText"
    ) {
      schemaMap[component.name] = createComponentValidationSchema(component);
    }
  });

  return z.object(schemaMap);
}

/**
 * Validate form data against component definitions
 */
export function validateFormData(
  data: Record<string, any>,
  components: FormComponent[]
): { success: boolean; errors?: Record<string, string> } {
  try {
    const schema = createFormValidationSchema(components);
    const result = schema.safeParse(data);

    if (result.success) {
      return { success: true };
    } else {
      const formattedErrors: Record<string, string> = {};

      // Format Zod errors into a more usable structure
      result.error.errors.forEach((err) => {
        const path = err.path.join(".");
        formattedErrors[path] = err.message;
      });

      return { success: false, errors: formattedErrors };
    }
  } catch (error) {
    console.error("Error validating form data:", error);
    return {
      success: false,
      errors: { _form: "An error occurred during validation" },
    };
  }
}
