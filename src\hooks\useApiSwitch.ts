import { useState, useEffect } from "react";
import { mockApi } from "@/lib/api/mock-api";
import { apiClient } from "@/lib/api/api-client";

// Define a union type for the API
export type ApiType = typeof mockApi | typeof apiClient;

// Environment variable to control API mode
const API_MODE = import.meta.env.VITE_API_MODE ?? "mock";

/**
 * Hook for switching between mock and real API
 * This allows for easy toggling between mock data and real API calls
 */
export function useApiSwitch() {
  // State to track current API mode
  const [apiMode, setApiMode] = useState<"mock" | "real">(
    API_MODE === "real" ? "real" : "mock"
  );

  // Effect to listen for API mode changes (e.g., from localStorage)
  useEffect(() => {
    const storedMode = localStorage.getItem("api_mode");
    if (storedMode === "real" || storedMode === "mock") {
      setApiMode(storedMode);
    }

    // Listen for changes to API mode
    const handleStorageChange = (e: StorageEvent) => {
      if (
        e.key === "api_mode" &&
        (e.newValue === "real" || e.newValue === "mock")
      ) {
        setApiMode(e.newValue);
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  // Function to toggle API mode
  const toggleApiMode = () => {
    const newMode = apiMode === "mock" ? "real" : "mock";
    setApiMode(newMode);
    localStorage.setItem("api_mode", newMode);
  };

  // Return the appropriate API client based on mode
  return {
    api:
      apiMode === "mock" ? mockApi : (apiClient as unknown as typeof mockApi),
    apiMode,
    toggleApiMode,
    isMockMode: apiMode === "mock",
  };
}
